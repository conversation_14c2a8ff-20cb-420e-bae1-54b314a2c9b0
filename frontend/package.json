{"name": "qtmaster-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "type-check": "tsc --noEmit", "lint": "tsc --noEmit"}, "dependencies": {"@tailwindcss/vite": "^4.1.12", "axios": "^1.4.0", "clsx": "^2.1.1", "framer-motion": "^12.23.12", "lucide-react": "^0.541.0", "prismjs": "^1.30.0", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^8.0.7", "react-router-dom": "^7.8.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.12"}, "devDependencies": {"@types/prismjs": "^1.26.5", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "typescript": "^5.9.2", "vite": "^5.2.0"}}
import { useState, useEffect, useRef } from "react";
import { Play, RotateCcw, <PERSON>, Eye } from "lucide-react";

type Tab = "html" | "css" | "js" | "preview";

const CodeCanvas = () => {
  const [html, setHtml] = useState(
    "<div>\n  <h1>Hello World!</h1>\n  <p>Edit this code to see changes</p>\n</div>",
  );
  const [css, setCss] = useState(
    "body {\n  font-family: sans-serif;\n  padding: 20px;\n}\n\nh1 {\n  color: #4a6cf7;\n}",
  );
  const [js, setJs] = useState(
    '// JavaScript code will go here\nconsole.log("Hello from JavaScript!");',
  );
  const [srcDoc, setSrcDoc] = useState("");
  const [activeTab, setActiveTab] = useState<Tab>("preview");
  const iframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    const timeout = setTimeout(() => {
      setSrcDoc(`
        <!DOCTYPE html>
        <html>
        <head>
          <style>${css}</style>
        </head>
        <body>
          ${html}
          <script>${js}</script>
        </body>
        </html>
      `);
    }, 250);

    return () => clearTimeout(timeout);
  }, [html, css, js]);

  const handleRunCode = () => {
    if (iframeRef.current) {
      iframeRef.current.srcdoc = srcDoc;
    }
  };

  const resetCode = () => {
    setHtml(
      "<div>\n  <h1>Hello World!</h1>\n  <p>Edit this code to see changes</p>\n</div>",
    );
    setCss(
      "body {\n  font-family: sans-serif;\n  padding: 20px;\n}\n\nh1 {\n  color: #4a6cf7;\n}",
    );
    setJs(
      '// JavaScript code will go here\nconsole.log("Hello from JavaScript!");',
    );
  };

  const getCurrentCode = () => {
    if (activeTab === "html") return html;
    if (activeTab === "css") return css;
    if (activeTab === "js") return js;
    return "";
  };

  const setCurrentCode = (value: string) => {
    if (activeTab === "html") setHtml(value);
    if (activeTab === "css") setCss(value);
    if (activeTab === "js") setJs(value);
  };

  const renderTabButton = (tab: Tab, label: string, icon: React.ReactNode) => (
    <button
      className={`px-4 py-2.5 font-medium transition-colors relative ${
        activeTab === tab
          ? "text-blue-600 bg-white border-b-2 border-blue-600"
          : "text-gray-600 hover:bg-gray-200"
      }`}
      onClick={() => setActiveTab(tab)}
    >
      {icon}
      {label}
    </button>
  );

  return (
    <div className="flex flex-col w-full h-[70vh] border border-gray-200 rounded-lg overflow-hidden bg-gray-50 font-sans">
      {/* Header */}
      <div className="flex justify-between items-center px-4 py-3 bg-white border-b border-gray-200">
        <h2 className="text-xl font-semibold text-gray-800 m-0">Interactive Code Canvas</h2>
        <div className="flex gap-2">
          <button
            onClick={handleRunCode}
            className="flex items-center gap-2 px-3 py-1.5 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors font-medium"
          >
            <Play size={16} />
            Run Code
          </button>
          <button
            onClick={resetCode}
            className="flex items-center gap-2 px-3 py-1.5 bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors font-medium"
          >
            <RotateCcw size={16} />
            Reset
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex bg-gray-100 border-b border-gray-200">
        {renderTabButton("html", "HTML", <Code size={16} className="inline mr-2" />)}
        {renderTabButton("css", "CSS", <Code size={16} className="inline mr-2" />)}
        {renderTabButton("js", "JavaScript", <Code size={16} className="inline mr-2" />)}
        {renderTabButton("preview", "Preview", <Eye size={16} className="inline mr-2" />)}
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden flex flex-col">
        {(activeTab === "html" || activeTab === "css" || activeTab === "js") && (
          <div className="flex flex-col h-full">
            <div className="px-4 py-2 bg-gray-50 border-b border-gray-200 text-sm text-gray-600">
              {activeTab.toUpperCase()} Editor
            </div>
            <div className="flex-1 relative">
              <textarea
                className="absolute inset-0 w-full h-full p-4 font-mono text-sm leading-relaxed border-none resize-none outline-none bg-white text-gray-800 overflow-auto"
                value={getCurrentCode()}
                onChange={(e) => setCurrentCode(e.target.value)}
                spellCheck="false"
                placeholder={`Enter your ${activeTab.toUpperCase()} code here...`}
              />
            </div>
          </div>
        )}

        {activeTab === "preview" && (
          <div className="flex flex-col h-full">
            <div className="px-4 py-2 bg-gray-50 border-b border-gray-200 text-sm text-gray-600">
              Live Preview
            </div>
            <div className="flex-1">
              <iframe
                ref={iframeRef}
                srcDoc={srcDoc}
                title="preview"
                sandbox="allow-scripts"
                className="w-full h-full bg-white border-none"
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CodeCanvas;